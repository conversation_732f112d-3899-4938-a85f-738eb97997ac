import React, { useMemo } from 'react';
import { Form, Select, Divider, InputNumber, Radio } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import styles from './index.module.less';
import { getSiteCurrency } from '@/utils/common';

interface SettingFormProps {
  form: any;
  initialValues?: any;
  showHeader?: boolean;
  country?: string;
  onValuesChange?: (...args: any[]) => void;
}

const ApproachBiasOptions = [
  { label: '保守', value: 'conservative' },
  { label: '平衡', value: 'balanced' },
  { label: '激进', value: 'aggressive' },
];

const SettingForm: React.FC<SettingFormProps> = ({
  form,
  initialValues,
  showHeader = false,
  country = 'US',
  onValuesChange
}) => {
  const formInitialValues = useMemo(() => {
    return initialValues || {
      day_target_sales: null,
      target_acos: null,
      daily_budget_suggest: null,
      approach_bias: null,
      daily_budget_range: { min: null, max: null },
      campaign_budget_range: { min: null, max: null },
      campaign_bid_range: { min: null, max: null },
      placement_bid_range: { min: null, max: null },
      hod_bid_range: { min: null, max: null },
    };
  }, [initialValues]);
  const currency = getSiteCurrency(country);
  const FormLayout = [
    {
      label: '天预算范围',
      value: 'daily_budget_range',
      prefix: currency,
      desc: '*天预算允许的范围，AI将在这个范围内调整每天的总预算'
    },
    {
      label: 'Campaign预算范围',
      value: 'campaign_budget_range',
      prefix: currency,
      desc: '*Campaign 预算允许的范围，AI将在这个范围内调整每个 Campaign 的预算'
    },
    {
      label: 'Campaign竞价调整范围',
      value: 'campaign_bid_range',
      suffix: '%',
      desc: '*调整 Campaign 竞价时， AI将在这个调整范围确定每个Campaign的最大调整比例（如 5%或-5%），AI在执行时，会在 0%～5%或-5%-0%之间根据Campaign中每个投放目标*（如关键词或ASIN）的实际表现调整其竞价。'
    },
    {
      label: '广告位竞价调整范围',
      value: 'placement_bid_range',
      suffix: '%',
      desc: '*广告位溢价允许的范围，AI将在这个范围内调整各个位置的溢价'
    },
    {
      label: '分时竞价范围',
      value: 'hod_bid_range',
      suffix: '%',
      desc: '*按小时调整竞价时，AI将在这个调整范围确定每小时具体的调整比例。注意分时竞价和Campaign 竞价会叠加处理，例如，某个Campaign竞价调整为 10%，假设在 12:00时的分时竞价调整为 10%， AI在实际调整时，将对该Campaign中所有投放目标的竞价上调 110%*110% =  121%，即原竞价为 1时，调整后竞价为 1 * 121% = 1.21'
    },
  ];
  return (
    <Form
      form={form}
      layout="vertical"
      className={styles.settingForm}
      initialValues={formInitialValues}
      onValuesChange={onValuesChange}
    >
      {showHeader && (
        <div className={styles.colTitle}>
          <span>参数设置</span>
          <QuestionCircleOutlined className={styles.questionIcon} />
        </div>
      )}
      <div className={styles.settingWrapper}>
        <div className={styles.settingCard}>
          <div className={styles.settingCardTitle}>
            <span>目标</span>
          </div>
          <Divider type="vertical" className={styles.settingCardDivider} />
          <div className={styles.targetWrapper}>
            <div className={styles.targetItem}>
              <div className={styles.targetItemTitle}>日均销售额:</div>
              <div className={styles.targetItemValue}>
                <Form.Item
                  name="day_target_sales"
                  className={styles.settingCardInput}
                  rules={[
                    { type: 'number' as const, min: 1, message: '最小值不能小于1' },
                    { type: 'integer' as const, message: '必须为整数' },
                  ]}
                >
                  <InputNumber
                    placeholder="日均销售额"
                    min={1}
                    precision={0}
                    style={{ width: '100%' }}
                    prefix={currency}
                  />
                </Form.Item>
              </div>
            </div>
            <div className={styles.targetItem}>
              <div className={styles.targetItemTitle}>目标ACoS:</div>
              <div className={styles.targetItemValue}>
                <Form.Item
                  name="target_acos"
                  className={styles.settingCardInput}
                  rules={[
                    { type: 'number' as const, min: 1, message: '最小值不能小于1' },
                    { type: 'integer' as const, message: '必须为整数' },
                  ]}
                >
                  <InputNumber
                    placeholder="ACoS"
                    min={1}
                    precision={0}
                    style={{ width: '100%' }}
                    suffix={'%'}
                  />
                </Form.Item>
              </div>
            </div>
          </div>
        </div>
        <div className={styles.settingCard}>
          <div className={styles.settingCardTitle}>
            <span>操盘风格</span>
          </div>
          <Divider type="vertical" className={styles.settingCardDivider} />
          <div className={styles.targetWrapper}>
            <div className={styles.targetItem}>
              <div className={styles.targetItemValue}>
                <Form.Item
                  name="approach_bias"
                  className={styles.settingCardInput}
                  style={{ width: 180 }}
                  rules={[
                    { required: true, message: '请选择操盘风格' },
                  ]}
                >
                  <Radio.Group
                    options={ApproachBiasOptions}
                    optionType="button"
                    buttonStyle="solid"
                  />
                </Form.Item>
              </div>
            </div>
          </div>
          <div className={styles.settingCardDesc}>
            <span>*保守：优先实现ACoS 目标；平衡：兼顾销售额目标和ACoS 目标；激进：优先实现销售额目标</span>
          </div>
        </div>
        <div className={styles.settingCard}>
          <div className={styles.settingCardTitle}>
            <span>预算</span>
          </div>
          <Divider type="vertical" className={styles.settingCardDivider} />
          <div className={styles.targetWrapper}>
            <div className={styles.targetItem}>
              <div className={styles.targetItemTitle}>日均预算:</div>
              <div className={styles.targetItemValue}>
                <Form.Item
                  name="daily_budget_suggest"
                  className={styles.settingCardInput}
                  rules={[
                    { type: 'number' as const, min: 1, message: '最小值不能小于1' },
                    { type: 'integer' as const, message: '必须为整数' },
                  ]}
                >
                  <InputNumber
                    placeholder="日均预算"
                    min={1}
                    precision={0}
                    style={{ width: '100%' }}
                    prefix={currency}
                  />
                </Form.Item>
              </div>
            </div>
          </div>
        </div>
        {
          FormLayout.map((item) => {
            // 判断是否是预算类
            const isBudget = item.value === 'daily_budget_range' || item.value === 'campaign_budget_range';
            return (
              <div className={styles.settingCard} key={item.value}>
                <div className={styles.settingCardTitle}>
                  <span>{item.label}</span>
                </div>
                <Divider type="vertical" className={styles.settingCardDivider} />
                <div className={styles.settingCardInputWrapper}>
                  <Form.Item
                    name={[item.value, 'min']}
                    className={styles.settingCardInput}
                    validateTrigger={['onChange', 'onBlur']}
                    dependencies={[[item.value, 'max']]}
                    rules={[
                      ...(isBudget
                        ? [
                            { required: true, message: '最小值必须填写' },
                            { type: 'number' as const, min: 1, message: '最小值不能小于1' },
                            { type: 'integer' as const, message: '必须为整数' },
                            {
                              validator: (_: any, value: any) => {
                                const max = form.getFieldValue([item.value, 'max']);
                                if (typeof value === 'number' && typeof max === 'number') {
                                  if (value >= max) {
                                    return Promise.reject('最小值必须小于最大值');
                                  } else {
                                    return Promise.resolve();
                                  }
                                } else {
                                  return Promise.resolve();
                                }
                              },
                            },
                          ]
                        : [
                            { required: true, message: '最小值必须填写' },
                            { type: 'number' as const, message: '必须为数字' },
                            { type: 'integer' as const, message: '必须为整数' },
                            {
                              validator: (_: any, value: any) => {
                                const max = form.getFieldValue([item.value, 'max']);
                                if (typeof value === 'number' && typeof max === 'number') {
                                  if (value >= max) {
                                    return Promise.reject('最小值必须小于最大值');
                                  } else {
                                    return Promise.resolve();
                                  }
                                } else {
                                  return Promise.resolve();
                                }
                              },
                            },
                          ]),
                    ]}
                  >
                    {isBudget ? (
                      <InputNumber
                        placeholder="最小值"
                        min={1}
                        precision={0}
                        style={{ width: '100%' }}
                        prefix={item.prefix ? item.prefix : undefined}
                        suffix={item.suffix ? item.suffix : undefined}
                      />
                    ) : (
                      <InputNumber
                        placeholder="最小值"
                        precision={0}
                        style={{ width: '100%' }}
                        prefix={item.prefix ? item.prefix : undefined}
                        suffix={item.suffix ? item.suffix : undefined}
                      />
                    )}
                  </Form.Item>
                  <span>~</span>
                  <Form.Item
                    name={[item.value, 'max']}
                    className={styles.settingCardInput}
                    validateTrigger={['onChange', 'onBlur']}
                    dependencies={[[item.value, 'min']]}
                    rules={[
                      ...(isBudget
                        ? [
                            { type: 'integer' as const, message: '必须为整数' },
                            {
                              validator: (_: any, value: any) => {
                                const min = form.getFieldValue([item.value, 'min']);
                                if (typeof value === 'number' && typeof min === 'number') {
                                  if (value <= min) {
                                    return Promise.reject('最大值必须大于最小值');
                                  } else {
                                    return Promise.resolve();
                                  }
                                } else {
                                  return Promise.resolve();
                                }
                              },
                            },
                          ]
                        : [
                            { required: true, message: '最大值必须填写' },
                            { type: 'integer' as const, message: '必须为整数' },
                            {
                              validator: (_: any, value: any) => {
                                const min = form.getFieldValue([item.value, 'min']);
                                if (typeof value === 'number' && typeof min === 'number') {
                                  if (value <= min) {
                                    return Promise.reject('最大值必须大于最小值');
                                  } else {
                                    return Promise.resolve();
                                  }
                                } else {
                                  return Promise.resolve();
                                }
                              },
                            },
                          ]),
                    ]}
                  >
                    <InputNumber
                      placeholder="最大值"
                      precision={0}
                      style={{ width: '100%' }}
                      prefix={item.prefix ? item.prefix : undefined}
                      suffix={item.suffix ? item.suffix : undefined}
                    />
                  </Form.Item>
                </div>
                <div className={styles.settingCardDesc}>
                  {item.desc}
                </div>
              </div>
            );
          })
        }
      </div>
    </Form>
  );
};

export default SettingForm;
