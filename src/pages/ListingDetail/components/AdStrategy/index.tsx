import { getAdStrategy } from '@/services/ibidder_api/operation';
import { Button, Typography, Segmented } from 'antd';
import React, { useEffect, useState } from 'react';
import { useSearchParams, useModel } from '@umijs/max';
import styles from './style.less';
import WeekStrategyContent from '../AiWork/components/weekStrategy';
import { getCountryTimezone, getDayOfWeek } from '@/utils/bus';
import WeekMonthAnalysisContent from '../AiWork/components/weekAnalysis';
import DayStrategyContent from '../AiWork/components/dayStragegy';
import IMGcustomizing from '@/assets/images/customizing.svg';
import { EditOutlined } from '@ant-design/icons';
import type {
  DayStrategyModalData,
  WeekStrategyModalData,
  WeekMonthReportModalData
} from '@/models/globalModals';
import { Loading } from '@/components';
const { Text } = Typography;

interface AdStrategyProps {
  onDocIdListUpdate: (docIdList: string[]) => void;
}

const AdStrategy: React.FC<AdStrategyProps> = ({ onDocIdListUpdate }) => {
  const { refreshCount } = useModel('strategyRefresh');
  const [searchParams] = useSearchParams();
  const parent_asin = searchParams.get('asin') as string;
  const profile_id = searchParams.get('profile_id') as string;
  const { productInfo } = useModel('productInfo');
  const country = productInfo!.country;
  const [loading, setLoading] = useState(false);

  // 全局弹框状态管理
  const {
    openDayStrategyModal,
    openWeekStrategyModal,
    openMonthReportModal
  } = useModel('globalModals');
  const [monthStrategyData, setMonthStrategyData] = useState<API.GetAdStrategyResponseData<{ market_report_month: Strategy.WeekAnalysisData }> | null>(null);
  const [weekStrategyData, setWeekStrategyData] = useState<API.GetAdStrategyResponseData<{ ads_strategy_week: Strategy.WeekStrategyData, real_ads_result: Strategy.Real_ads_result }> | null>(null);
  const [dayStrategyData, setDayStrategyData] = useState<API.GetAdStrategyResponseData<{ ads_strategy_day: Strategy.DayStrategyData, real_ads_result: Strategy.Real_ads_result }> | null>(null);
  const [hasMonthReport, setHasMonthReport] = useState(true)
  const [hasWeekReport, setHasWeekReport] = useState(true)
  const [activeTab, setActiveTab] = useState<'day' | 'week' | 'month'>('day');

  const getStrategyData = async () => {
    setLoading(true);
    try {
      const results = await Promise.all([
        getAdStrategy({
          date: getCountryTimezone(country, 'YYYY-MM-DD'),
          job_id: 'market_report_month',
          asin: parent_asin,
          profile_id,
        }).catch(e => e.response?.data || { code: e.code || 500, message: e.message, data: {} }),
        getAdStrategy({
          date: getCountryTimezone(country, 'YYYY-MM-DD'),
          job_id: 'ads_strategy_week',
          asin: parent_asin,
          profile_id,
        }).catch(e => e.response?.data || { code: e.code || 500, message: e.message, data: {} }),
        getAdStrategy({
          date: getCountryTimezone(country, 'YYYY-MM-DD'),
          job_id: 'ads_strategy_day',
          asin: parent_asin,
          profile_id,
        }).catch(e => e.response?.data || { code: e.code || 500, message: e.message, data: {} }),
      ]);

      const [monthRes, weekRes, dayRes] = results as [any, any, any];
      let monthData = null;
      let weekData = null;
      let dayData = null;

      if (monthRes.code === 200) {
        setMonthStrategyData(monthRes.data);
        monthData = monthRes.data;
      }

      if (weekRes.code === 200) {
        setWeekStrategyData(weekRes.data);
        weekData = weekRes.data;
        setHasMonthReport(true)
      }

      if (dayRes.code === 200) {
        setDayStrategyData(dayRes.data);
        dayData = dayRes.data;
        setHasWeekReport(true)
      }

      onDocIdListUpdate([monthData?.es_id, weekData?.es_id, dayData?.es_id].filter(Boolean) as string[]);
    } finally {
      setLoading(false);
    }
  }

  // 获取策略看板数据
  useEffect(() => {
    getStrategyData();
  }, [parent_asin, refreshCount]);

  const market_report_month = monthStrategyData?.result?.market_report_month;
  const ads_strategy_week = weekStrategyData?.result?.ads_strategy_week;
  const ads_strategy_day = dayStrategyData?.result?.ads_strategy_day;

  const viewMonthStrategy = () => {
    if (monthStrategyData) {
      const title = `月市场分析报告（${market_report_month?.start_date ?? ''}~${market_report_month?.end_date ?? ''}）`;
      const modalData: WeekMonthReportModalData = {
        current_time: monthStrategyData.current_time,
        job_id: monthStrategyData.job_id,
      };
      openMonthReportModal(modalData, title);
    }
  };

  const viewCompleteStrategy = () => {
    if (weekStrategyData) {
      const title = `周广告投放策略（${ads_strategy_week?.start_date ?? ''}~${ads_strategy_week?.end_date ?? ''}）`;
      const modalData: WeekStrategyModalData = {
        date: getCountryTimezone(country, 'YYYY-MM-DD'),
        current_time: weekStrategyData.current_time,
        job_id: weekStrategyData.job_id,
        isCompleteStrategy: true,
      };
      openWeekStrategyModal(modalData, title);
    }
  };

  const viewDayStrategy = () => {
    if (dayStrategyData) {
      const title = `日广告投放策略（${ads_strategy_day?.date ?? ''} ${getDayOfWeek(ads_strategy_day?.date)}）`;
      const modalData: DayStrategyModalData = {
        current_time: dayStrategyData.current_time,
        job_id: dayStrategyData.job_id,
        date: dayStrategyData?.target_week ?? getCountryTimezone(country, 'YYYY-MM-DD'),
      };
      openDayStrategyModal(modalData, title);
    }
  };

  const renderContent = () => {
    switch (activeTab) {
      case 'month':
        return (
          <div>
            {monthStrategyData && (
              <WeekMonthAnalysisContent
                type='month'
                key={monthStrategyData.es_id + monthStrategyData.current_time}
                current_time={monthStrategyData.current_time}
                job_id={monthStrategyData.job_id}
                onCancel={() => { }}
                onSuccess={() => { }}
                controlBtn={
                  monthStrategyData?.can_edit === true &&
                  <Button onClick={viewMonthStrategy}><EditOutlined />修改策略</Button>
                }
                showReviewArea={false}
              />
            )}
          </div>
        );
      case 'week':
        return (
          weekStrategyData === null ?
            <div className={styles.reportGoToReview} >
              <img src={IMGcustomizing} />
              {hasMonthReport === false ? <Text>请先审核月市场报告</Text> : <Text>周广告策略正在制定中，请稍后～</Text>}
              {hasMonthReport === false ? <Button type='primary' onClick={monthStrategyData?.can_edit === true ? viewMonthStrategy : undefined} >去审核</Button> : null}
            </div>
            :
            <div>
              {weekStrategyData && (
                <WeekStrategyContent
                  key={weekStrategyData.es_id + weekStrategyData.current_time}
                  date={getCountryTimezone(country, 'YYYY-MM-DD')}
                  current_time={weekStrategyData.current_time}
                  job_id={weekStrategyData.job_id}
                  isCompleteStrategy={true}
                  onCancel={() => { }}
                  onSuccess={() => { }}
                  controlBtn={
                    weekStrategyData?.can_edit === true &&
                    <Button onClick={viewCompleteStrategy}><EditOutlined />修改策略</Button>
                  }
                  showReviewArea={false}
                />
              )}
            </div>
        );
      case 'day':
        return (
          dayStrategyData === null ?
            <div className={styles.reportGoToReview} >
              <img src={IMGcustomizing} />
              {hasMonthReport === false ? <Text>请先审核月市场报告</Text> :
                hasWeekReport === false ? <Text>请先审核周广告策略</Text> : <Text>日广告策略正在制定中，请稍后～</Text>}
              {
                hasMonthReport === false ? <Button type='primary' onClick={monthStrategyData?.can_edit === true ? viewMonthStrategy : undefined} >去审核</Button> :
                  hasWeekReport === false ? <Button type='primary' onClick={weekStrategyData?.can_edit === true ? viewCompleteStrategy : undefined} >去审核</Button> : null
              }
            </div>
            :
            <div>
              {dayStrategyData && (
                <DayStrategyContent
                  key={dayStrategyData.es_id + dayStrategyData.current_time}
                  current_time={dayStrategyData.current_time}
                  job_id={dayStrategyData.job_id}
                  onCancel={() => { }}
                  onSuccess={() => { }}
                  date={dayStrategyData.target_week ?? getCountryTimezone(country, 'YYYY-MM-DD')}
                  controlBtn={
                    dayStrategyData?.can_edit === true &&
                    <Button onClick={viewDayStrategy}><EditOutlined />修改策略</Button>
                  }
                  showReviewArea={false}
                />
              )}
            </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className={styles.container}>
      <div className={styles.viewSwitcher}>
        <Segmented
          block
          size='large'
          value={activeTab}
          onChange={(value) => setActiveTab(value as 'day' | 'week' | 'month')}
          options={[
            { label: `今日 ${dayStrategyData?.target_week ? `(${dayStrategyData.target_week} ${getDayOfWeek(dayStrategyData.target_week)})` : ''}`, value: 'day' },
            { label: `本周 ${ads_strategy_week?.start_date && ads_strategy_week?.end_date ? `(${ads_strategy_week.start_date}~${ads_strategy_week.end_date})` : ''}`, value: 'week' },
            { label: `本月 ${market_report_month?.start_date && market_report_month?.end_date ? `(${market_report_month.start_date}~${market_report_month.end_date})` : ''}`, value: 'month' },
          ]}
        />
      </div>

      {/* 内容区域 */}
      <div className={styles.renderContent}>
        {loading ? <Loading /> : renderContent()}
      </div>


    </div>
  );
};

export default AdStrategy;
