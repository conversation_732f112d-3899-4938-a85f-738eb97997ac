import React, { useEffect, useMemo, useState } from 'react';
import { Card, DatePicker, Flex, Row, Col, Typography, Spin, Tooltip, Divider } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { useModel } from '@umijs/max';
import { getAdsTrend } from '@/services/ibidder_api/operation';
import AdsTrendChart from '../AiWork/components/AdsTrendChart';
import styles from './style.less';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { getBudgetColor } from '@/utils/bus';



const { Title, Text } = Typography;
const { RangePicker } = DatePicker;

type AdsTrendCardProps = {
  style?: React.CSSProperties;
  type: 'day' | 'week' | 'month';
};

type VisibleState = {
  spend: boolean;
  sales: boolean;
  acos: boolean;
  cvr: boolean;
};

const toNumber = (v: any): number => {
  if (v === null || v === undefined || v === '') return 0;
  const s = String(v).replace('%', '');
  const n = Number(s);
  return Number.isFinite(n) ? n : 0;
};

const formatCurrency = (currency: string, value: number | string) => `${currency}${Number(value || 0).toFixed(2)}`;



const computePresetRange = (type: 'day' | 'week' | 'month', count: number): [Dayjs, Dayjs] => {
  const now = dayjs();
  if (type === 'day') {
    const end = now.endOf('day');
    const start = end.subtract(count - 1, 'day').startOf('day');
    return [start, end];
  }
  if (type === 'week') {
    // Use Monday-Sunday as display, but for request we only need date range
    const end = now.endOf('day');
    const start = end.subtract(count * 7 - 1, 'day').startOf('day');
    return [start, end];
  }
  // month
  const end = now.endOf('day');
  const start = now.startOf('month').subtract(count - 1, 'month');
  return [start, end];
};

const AdsTrendCard: React.FC<AdsTrendCardProps> = (props) => {
  const { productInfo } = useModel('productInfo');
  const currency = productInfo?.currency || '$';
  const asins = productInfo?.asins || [];
  const parent_asin = productInfo?.parent_asin || '';
  const profile_id = productInfo?.profile_id as number | undefined;

  const { type } = props;
  const [loading, setLoading] = useState(false);
  const [range, setRange] = useState<[Dayjs, Dayjs]>(() => {
    if (type === 'day') return computePresetRange('day', 14);
    if (type === 'week') return computePresetRange('week', 14);
    return computePresetRange('month', 6);
  });
  const [rangeForChart, setRangeForChart] = useState(range);

  const [data, setData] = useState<API.AdsTrendResponse | null>(null);
  const [visible, setVisible] = useState<VisibleState>({ spend: true, sales: true, acos: true, cvr: true });

  const presets = useMemo(() => {
    if (type === 'day') {
      const opts = [3, 7, 14, 30, 60, 90, 180, 365];
      return opts.map(n => ({ label: `近${n}天`, value: computePresetRange('day', n) }));
    }
    if (type === 'week') {
      const opts = [14, 28, 56];
      return opts.map(n => ({ label: `近${n}周`, value: computePresetRange('week', n) }));
    }
    const opts = [3, 6, 12];
    return opts.map(n => ({ label: `近${n}个月`, value: computePresetRange('month', n) }));
  }, [type]);

  const fetchData = async (start: Dayjs, end: Dayjs) => {
    if (!profile_id || !parent_asin) return;
    setLoading(true);
    try {
      // For week and month types, adjust start/end times to period boundaries
      let apiStartTime: string;
      let apiEndTime: string;
      
      if (type === 'week') {
        // For week: start should be Monday of the start week, end should be Sunday of the end week
        const startWeekBegin = start.startOf('week'); // Monday
        const endWeekEnd = end.endOf('week'); // Sunday
        apiStartTime = startWeekBegin.format('YYYY-MM-DD');
        apiEndTime = endWeekEnd.format('YYYY-MM-DD');
      } else if (type === 'month') {
        // For month: start should be 1st day of start month, end should be last day of end month
        const startMonthBegin = start.startOf('month');
        const endMonthEnd = end.endOf('month');
        apiStartTime = startMonthBegin.format('YYYY-MM-DD');
        apiEndTime = endMonthEnd.format('YYYY-MM-DD');
      } else {
        // For day type, use original logic
        apiStartTime = start.format('YYYY-MM-DD');
        apiEndTime = end.format('YYYY-MM-DD');
      }
      
      const res = await getAdsTrend({
        asins,
        parent_asin,
        profile_id: Number(profile_id),
        start_time: apiStartTime,
        end_time: apiEndTime,
        type,
      });
      // @ts-ignore
      if (res && (res.code === 200 || res.list || res.sum)) {
        // Some request util may return data directly
        const payload: API.AdsTrendResponse = (res.data || res) as any;
        setData(payload);
        setRangeForChart([start, end]);
      } else {
        setData(null);
        setRangeForChart([start, end]);
      }
    } catch (e) {
      setData(null);
      setRangeForChart([start, end]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (range) fetchData(range[0], range[1]);
    // reset visible when type changes
    setVisible({ spend: true, sales: true, acos: true, cvr: true });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [type, range?.[0]?.valueOf(), range?.[1]?.valueOf(), profile_id, parent_asin]);

  const chartData = useMemo(() => {
    if (
      range?.[0]?.valueOf() !== rangeForChart?.[0]?.valueOf() ||
      range?.[1]?.valueOf() !== rangeForChart?.[1]?.valueOf()
    ) {
      return {
        xAxisData: [],
        spend: [],
        sales: [],
        acos: [],
        cvr: [],
      };
    }

    const xAxisData: string[] = [];
    const spend: number[] = [];
    const sales: number[] = [];
    const acos: number[] = [];
    const cvr: number[] = [];

    if (data?.list?.length) {
      data.list.forEach((item) => {
        const startDate = item.startDate;
        if (type === 'week') {
          const startD = dayjs(startDate);
          const endD = startD.add(6, 'day');
          xAxisData.push(`${startD.format('YYYY-MM-DD')}～${endD.format('YYYY-MM-DD')}`);
        } else if (type === 'month') {
          xAxisData.push(dayjs(startDate).format('YYYY-MM'));
        } else {
          xAxisData.push(startDate);
        }
        spend.push(Number(item.spend || 0));
        sales.push(Number(item.sales || 0));
        acos.push(toNumber(item.acos));
        cvr.push(toNumber(item.cvr));
      });
    }

    return {
      xAxisData,
      spend: visible.spend ? spend : [],
      sales: visible.sales ? sales : [],
      acos: visible.acos ? acos : [],
      cvr: visible.cvr ? cvr : [],
    };
  }, [data, type, visible, range, rangeForChart]);

  const keyForChart = useMemo(() => {
    // Force re-mount to update options due to AdsTrendChart's internal effect deps
    return `${type}-${rangeForChart?.[0]?.valueOf()}-${rangeForChart?.[1]?.valueOf()}-${visible.spend}-${visible.sales}-${visible.acos}-${visible.cvr}`;
  }, [type, rangeForChart, visible]);

  const sum = data?.sum;
  const prev = data?.previous_sum as any;

  type Metric = {
    id: keyof VisibleState;
    title: string;
    desc: string;
    color: string;
    value: string;
    prevValue: string;
    ratio?: string;
    onClick: () => void;
  };

  const MetricCard = ({ id, title, desc, color, value, prevValue, ratio, onClick }: Metric) => (
    <Col xs={12} md={6}>
      <div
        onClick={onClick}
        className={styles.metricCard}
        style={{
          background: visible[id] ? '#fff' : '#F9FAFB',
          borderRadius: '4px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.09)',
          padding: '16px 20px',
          display: 'flex',
          alignItems: 'center',
          cursor: 'pointer',
          gap: '16px',
          borderLeft: `4px solid ${visible[id] ? color : '#F9FAFB'}`,
        }}
      >
        <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
          <Text style={{ color: '#595959', fontSize: '14px' }}>{title}
            <Tooltip title={desc} overlayInnerStyle={{ whiteSpace: 'pre-wrap', width: 500 }}>
              <QuestionCircleOutlined style={{ color: '#86909C', fontSize: '14px', marginLeft: '4px' }} />
            </Tooltip>
          </Text>
          <div style={{ fontWeight: 'bold', fontSize: '24px', lineHeight: '1.2' }}>
            {value}
          </div>
          <Tooltip
            overlayInnerStyle={{ width: 450 }}
            placement="bottom"
            title={
              <div style={{ whiteSpace: 'nowrap', display: 'flex', flexDirection: 'column', gap: 10, padding: 8 }}>
                <div>{title}</div>
                <div style={{ display: 'flex', gap: 20 }}>
                  <span>本期：{value}</span>
                  <span>{data?.sum?.start_time}~{data?.sum?.end_time}</span>
                </div>
                <div style={{ display: 'flex', gap: 20 }}>
                  <span>环比：{prevValue}</span>
                  <span>{data?.previous_sum?.start_time}~{data?.previous_sum?.end_time}</span>
                  <span style={{ color: getBudgetColor(0, ratio || 0) }}>
                    {ratio ? ratio.startsWith('-') ? ratio : '+' + ratio : '-'}
                  </span>
                </div>
              </div>
            }
          >
            <div style={{ color: '#8c8c8c', fontSize: '12px', display: 'flex', alignItems: 'center', gap: '8px' }}>
              <span>{prevValue}</span>
              <Divider type="vertical" />
              <span style={{ color: getBudgetColor(0, ratio || 0) }}>
                {ratio ? ratio.startsWith('-') ? ratio : '+' + ratio : '-'}
              </span>
            </div>
          </Tooltip>
        </div>
      </div>
    </Col>
  );

  const metrics: Metric[] = [
    {
      id: 'spend',
      title: '广告花费',
      color: '#5BC4FF',
      desc: `广告活动的点击或展示总费用。

特别说明：
1、一旦识别出无效点击，亚马逊最多会在 3 天内从您的支出统计数据中删除这些点击记录。日期范围（含过去 3 天内的支出）可能因点击和支出失效而有所调整；
2、因近30天（尤其近3天）亚马逊接口返回的数据，可能与亚马逊控制台展现的数据存在略微不一致，因此可能导致系统统计结果与亚马逊控制台展现的数据存在略微不一致。`,
      value: formatCurrency(currency, sum?.spend || 0),
      prevValue: formatCurrency(currency, prev?.spend || 0),
      ratio: prev?.spend_ratio,
      onClick: () => setVisible(v => ({ ...v, spend: !v.spend })),
    },
    {
      id: 'sales',
      title: '广告销售额',
      color: '#FFD66B',
      desc: `广告销售额是在某种广告活动投放期间的指定时间范围内，因广告被点击或浏览而向顾客售出的商品的价值总额。

商品推广销售额： 7 天内售出的推广商品及库存中其他商品的销售额。
品牌推广销售额： 14 天内在亚马逊及第三方卖家处售出的推广商品及同品牌其他商品的销售额。
展示型推广销售额： 14 天内售出的推广商品及库存中其他商品的销售额。

您的销售数据最长可能需要 12 小时才会更新。 因此，“今天”日期范围内的销售数据可能会延迟。 我们建议您等待所有销售数据填充完毕后再评估广告活动绩效。 
未成功支付的款项和 72 小时内取消的订单的金额将从销售总额中删除。`,
      value: formatCurrency(currency, sum?.sales || 0),
      prevValue: formatCurrency(currency, prev?.sales || 0),
      ratio: prev?.sales_ratio,
      onClick: () => setVisible(v => ({ ...v, sales: !v.sales })),
    },
    {
      id: 'acos',
      title: 'ACOS',
      color: '#AE8FF7',
      desc: `ACoS是在制定时间范围内，某种类型的广告活动因广告被点击而产生的支出在由广告产生的销售额中所占的百分比。

计算规则：ACoS = 广告花费 / 广告销售额 * 100%；

商品推广广告：ACoS包括 7 天内售出的推广商品及库存中其他商品的销售额。
品牌推广： ACoS包括14 天内在亚马逊及第三方卖家处售出的推广商品及同品牌其他商品的销售额。
展示型推广销售额： ACoS 包括14 天内售出的推广商品及库存中其他商品的销售额。

您的销售数据最长可能需要 12 小时才会更新。 因此，“今天”日期范围内的销售数据可能会延迟。 我们建议您等待所有销售数据填充完毕后再评估广告活动绩效。
未成功支付的款项和 72 小时内取消的订单的金额将从销售总额中删除。`,
      value: sum?.acos || '-',
      prevValue: prev?.acos || '-',
      ratio: prev?.acos_ratio,
      onClick: () => setVisible(v => ({ ...v, acos: !v.acos })),
    },
    {
      id: 'cvr',
      title: 'CVR',
      color: '#1DB88C',
      desc: `每一次点击带来订单的概率。

计算规则：CVR = 订单数 / 点击量 * 100%；
例如：CVR为10%，每100次点击，能带来10笔广告订单。

亚马逊广告，CVR一般为5-10%左右，
CVR的重要影响因素：商品图片、卖点、评论内容、促销活动，
如果CVR低，建议您优化listing，根据商品的受众人群，
提炼卖点、设计精美的场景图、善用Q&A，Review等。`,
      value: sum?.cvr || '-',
      prevValue: prev?.cvr || '-',
      ratio: prev?.cvr_ratio,
      onClick: () => setVisible(v => ({ ...v, cvr: !v.cvr })),
    },
  ];

  const metricCards = (
    <Row gutter={[16, 16]}>
      {metrics.map(metric => (
        <MetricCard {...metric} key={metric.id} />
      ))}
    </Row>
  );

  return (
    <Card data-testid="ads-trend-card" className="card" style={{ ...props.style }}>
      <Flex justify="space-between" align="center" style={{ marginBottom: 24 }}>
        <Title level={4} style={{ margin: 0 }}>广告走势</Title>
        <RangePicker
          allowClear={false}
          value={range}
          onChange={(v) => {
            if (!v || v.length !== 2) return;
            setRange([v[0]!, v[1]!]);
          }}
          presets={presets as any}
          picker={type === 'day' ? 'date' : type}
        />
      </Flex>

      {metricCards}

      <Spin spinning={loading}>
        <div style={{ width: '100%', height: 300 }}>
          <AdsTrendChart
            key={keyForChart}
            currency={currency}
            data={chartData}
            hideLegend
            chartOption={{
              axisLabel: type === 'week' ? { interval: 0 } : {},
            }}
          />
        </div>
      </Spin>
    </Card>
  );
};

export default AdsTrendCard;

