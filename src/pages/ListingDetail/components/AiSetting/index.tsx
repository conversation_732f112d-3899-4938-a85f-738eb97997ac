import React, { useState, useMemo, useEffect } from 'react';
import { Card, Divider, Switch, Modal, Form, Button, message } from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { getAvatarByType } from '../bus';
import styles from '../AiWork/style.less';
import { getListingSetting, updateListingSetting, updateListing } from '@/services/ibidder_api/listings';
import { useSearchParams, useRequest, useModel } from '@umijs/max';
import SettingForm from '@/pages/Admin/ListingManagement/components/SettingForm';
import isEqual from 'lodash/isEqual';


const AiSettingContainer: React.FC = () => {
  const [opened, setOpened] = useState(false);
  const [searchParams] = useSearchParams();
  const asin = searchParams.get('asin') as string;
  const profile_id = searchParams.get('profile_id') as string;
  const { productInfo, setProductInfoFn } = useModel('productInfo');
  const [form] = Form.useForm();
  const initialValues = useMemo(() => {
    return productInfo?.ads_rules;
  }, [productInfo]);

  // 新增：表单是否被修改的状态
  const [isFormChanged, setIsFormChanged] = useState(false);

  // 监听表单变化，判断是否和初始值不同
  // 改为onValuesChange方式
  const handleFormValuesChange = () => {
    const currentValues = form.getFieldsValue(true);
    setIsFormChanged(!isEqual(currentValues, initialValues || {
      day_target_sales: null,
      target_acos: null,
      daily_budget_suggest: null,
      approach_bias: null,
      daily_budget_range: { min: null, max: null },
      campaign_budget_range: { min: null, max: null },
      campaign_bid_range: { min: null, max: null },
      placement_bid_range: { min: null, max: null },
      hod_bid_range: { min: null, max: null },
    }));
  };

  // initialValues变化时，重置表单内容
  useEffect(() => {
    if (initialValues) {
      form.setFieldsValue(initialValues);
      // 变化时也要重置按钮状态
      setIsFormChanged(false);
    }
  }, [initialValues, form]);

  // 使用useRequest获取设置信息
  const { loading } = useRequest(
    () => getListingSetting({
      parent_asin: asin,
      profile_id: Number(profile_id),
    }),
    {
      ready: !!(asin && profile_id),
      refreshDeps: [asin, profile_id],
      onSuccess: (res) => {
        const ads_operation = res.setting?.ads_operation || false;
        setOpened(ads_operation);
      },
      onError: (error) => {
        console.error('获取设置信息失败:', error);
      }
    }
  );

  const handleSwitchChange = (checked: boolean) => {
    if (localStorage.getItem('OpenAiSettingsEnabled') === '开') {
      setOpened(checked);
      updateListingSetting({
        parent_asin: asin,
        profile_id: Number(profile_id),
        ads_operation: checked,
      })
    } else {
      // 显示权限提示框
      Modal.warning({
        title: '权限暂未开通，请联系您的主管',
        icon: <ExclamationCircleOutlined style={{ color: '#faad14' }} />,
        okText: '确定',
        okButtonProps: {
          style: {
            backgroundColor: '#ff4d4f',
            borderColor: '#ff4d4f',
            color: '#fff'
          }
        },
        centered: true,
      });
    }
  };

  const handleCancel = () => {
    form.setFieldsValue(initialValues || {
      day_target_sales: null,
      target_acos: null,
      daily_budget_suggest: null,
      approach_bias: null,
      daily_budget_range: { min: null, max: null },
      campaign_budget_range: { min: null, max: null },
      campaign_bid_range: { min: null, max: null },
      placement_bid_range: { min: null, max: null },
      hod_bid_range: { min: null, max: null },
    });
    handleFormValuesChange();
  };

  const handleSave = () => {
    form.validateFields().then((values: any) => {
      updateListing({
        parent_asin: asin,
        profile_id: Number(profile_id),
        ads_rules: values,
      })
        .then((res: any) => {
          if (res.code === 200) {
            message.success('保存成功');
            setProductInfoFn({
              ...productInfo!,
              ads_rules: res.data?.ads_rules,
            } as API.ListingInfo);
            handleFormValuesChange();
          } else {
            message.error(res.message || '保存失败');
          }
        })
        .catch((error: any) => {
          console.error('保存失败:', error);
        });
    });
  };

  return (
    <>
      <Card data-test-id="aisettings-switch" className="card" loading={loading}>
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16, marginTop: 16 }}>
          {getAvatarByType('operAgent')}
          <div className={styles.aiWorkRole}>
            <span className={styles.aiWorkType}>广告优化师</span>
            <span className={styles.aiWorkDesc}>ROI操盘手</span>
          </div>
          <Divider type="vertical" style={{ height: 48 }} />
          <div style={{ marginLeft: 24, marginRight: 16, color: '#000000' }}>AI执行广告操作开关：</div>
          <Switch
            checkedChildren="开"
            unCheckedChildren="关"
            checked={opened}
            onChange={handleSwitchChange}
            disabled={false}
          />
          <div style={{ color: '#979797', marginLeft: 16 }}>
            <span style={{ color: '#D80027' }}>*</span>
            开启后，AI将自动调整此产品的亚马逊广告
          </div>
        </div>
      </Card>
      <Card data-test-id="aisettings-form" className="card" loading={loading} style={{ marginTop: 16 }}>
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16, marginTop: 16 }}>
          {getAvatarByType('strategyAgent')}
          <div className={styles.aiWorkRole}>
            <span className={styles.aiWorkType}>广告策略师</span>
            <span className={styles.aiWorkDesc}>广告增长引擎</span>
          </div>
          <Divider type="vertical" style={{ height: 48 }} />
          <div style={{ marginLeft: 24, marginRight: 16, color: '#000000' }}>广告参数设置：</div>
          <div style={{ color: '#979797', marginLeft: 16 }}>
            <span style={{ color: '#D80027' }}>*</span>
            保存后，AI将按照您设置的调整范围自动调整此产品的亚马逊广告
          </div>
          {/* 只有表单被修改时才显示按钮 */}
          {isFormChanged && (
            <div style={{ marginLeft: 'auto', display: 'flex', gap: 16 }}>
              <Button type="default" onClick={handleCancel}>取消</Button>
              <Button type="primary" onClick={handleSave}>保存</Button>
            </div>
          )}
        </div>
        <div>
          <SettingForm
            form={form}
            initialValues={initialValues}
            onValuesChange={handleFormValuesChange}
            country={productInfo?.country}
          />
        </div>
      </Card>
    </>
  );
};

export default AiSettingContainer;
