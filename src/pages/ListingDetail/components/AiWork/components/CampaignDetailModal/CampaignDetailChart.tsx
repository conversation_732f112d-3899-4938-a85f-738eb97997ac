import React, { useEffect, useRef } from 'react';
import * as echarts from 'echarts';

type CampaignDetailChartProps = {
  currency: string;
  data: {
    xAxisData: string[];
    spend: number[];
    sales: number[];
    orders: number[];
    budget: number[];
    acos: number[];
    cvr: number[];
    ctr: number[];
    impressions: number[];
    clicks: number[];
    cpc: number[];
    cpa: number[];
    ad_sales_volume: number[];
    direct_orders: number[];
    direct_sales_amount: number[];
    spend_percentage: number[];
    sales_percentage: number[];
  };
};

const CampaignDetailChart: React.FC<CampaignDetailChartProps> = (props) => {
  const { currency, data } = props;
  const { 
    xAxisData = [], 
    spend = [], 
    sales = [], 
    orders = [],
    budget = [],
    acos = [], 
    cvr = [],
    ctr = [],
    impressions = [],
    clicks = [],
    cpc = [],
    cpa = [],
    ad_sales_volume = [],
    direct_orders = [],
    direct_sales_amount = [],
    spend_percentage = [],
    sales_percentage = [],
  } = data;
  const chartRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (chartRef.current) {
      const myChart = echarts.init(chartRef.current);

      const option: echarts.EChartsOption = {
        color: ['#5470C6', '#91CC75', '#FAC858', '#EE6666', '#73C0DE', '#3BA272', '#FC8452', '#9A60B4', '#EA7CCC', '#ff4d4f', '#ffc53d', '#ff7a45', '#ffa940', '#bfbfbf', '#434343'],
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0,0,0,0.65)',
          borderColor: '#444',
          textStyle: {
            color: '#fff',
          },
          formatter: (params: any) => {
            let result = `<div style="color:#fff; margin-bottom: 8px;">${params[0].axisValue}</div>`;
            const containerStyle = `display: grid; grid-template-columns: auto auto auto; align-items: center; grid-gap:4px; color: #fff;`;
            let gridContent = '';

            const seriesData: Record<string, any> = {};
            params.forEach((p: any) => {
                seriesData[p.seriesName] = p;
            });

            const seriesOrder = ['广告订单', '广告花费', '广告销售额', '预算', 'ACoS', 'CVR', 'CTR', '曝光量', '点击', 'CPC', 'CPA', '广告销量', '直接成交订单', '直接成交销售额', '花费百分比', '销售额百分比'];

            seriesOrder.forEach(seriesName => {
                const param = seriesData[seriesName];
                if (!param) return;

                const { value, color } = param;
                let formattedValue = value;

                if (['广告花费', '广告销售额', '预算', 'CPC', 'CPA', '直接成交销售额'].includes(seriesName)) {
                    formattedValue = `${currency}${value.toFixed(2)}`;
                } else if (['ACoS', 'CVR', 'CTR', '花费百分比', '销售额百分比'].includes(seriesName)) {
                    formattedValue = `${value.toFixed(2)}%`;
                }

                gridContent += `
                      <div><span style="color:${color};font-size:16px;">●</span> ${seriesName}</div>
                      <div>:</div>
                      <div style="text-align: right; font-weight: bold;">${formattedValue}</div>
                  `;
            });
            
            result += `<div style="${containerStyle}">${gridContent}</div>`;
            return result;
          }
        },
        legend: {
          data: ['广告订单', '广告花费', '广告销售额', '预算', 'ACoS', 'CVR', 'CTR', '曝光量', '点击', 'CPC', 'CPA', '广告销量', '直接成交订单', '直接成交销售额', '花费百分比', '销售额百分比'],
          right: '2%',
          top: 'top',
        },
        grid: {
          left: '2%',
          right: '2%',
          bottom: '1%',
          top: 60,
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: true,
          data: xAxisData,
        },
        yAxis: [
          {
            type: 'value',
            position: 'left',
            axisLabel: {
              formatter: '{value}',
            },
          },
          {
            type: 'value',
            position: 'right',
            axisLabel: {
              formatter: '{value} %',
            },
            splitLine: {
              show: false,
            },
          },
        ],
        series: [
          { name: '广告订单', type: 'bar', yAxisIndex: 0, data: orders, barMaxWidth: 30 },
          { name: '广告花费', type: 'bar', yAxisIndex: 0, data: spend, barMaxWidth: 30 },
          { name: '广告销售额', type: 'bar', yAxisIndex: 0, data: sales, barMaxWidth: 30 },
          { name: '预算', type: 'bar', yAxisIndex: 0, data: budget, barMaxWidth: 30 },
          { name: 'ACoS', type: 'line', yAxisIndex: 1, data: acos, showSymbol: false },
          { name: 'CVR', type: 'line', yAxisIndex: 1, data: cvr, showSymbol: false },
          { name: 'CTR', type: 'line', yAxisIndex: 1, data: ctr, showSymbol: false },
          { name: '曝光量', type: 'line', yAxisIndex: 0, data: impressions, showSymbol: false },
          { name: '点击', type: 'line', yAxisIndex: 0, data: clicks, showSymbol: false },
          { name: 'CPC', type: 'line', yAxisIndex: 0, data: cpc, showSymbol: false },
          { name: 'CPA', type: 'line', yAxisIndex: 0, data: cpa, showSymbol: false },
          { name: '广告销量', type: 'line', yAxisIndex: 0, data: ad_sales_volume, showSymbol: false },
          { name: '直接成交订单', type: 'line', yAxisIndex: 0, data: direct_orders, showSymbol: false },
          { name: '直接成交销售额', type: 'line', yAxisIndex: 0, data: direct_sales_amount, showSymbol: false },
          { name: '花费百分比', type: 'line', yAxisIndex: 1, data: spend_percentage, showSymbol: false },
          { name: '销售额百分比', type: 'line', yAxisIndex: 1, data: sales_percentage, showSymbol: false },
        ],
      };

      myChart.setOption(option);

      const resizeHandler = () => myChart.resize();
      window.addEventListener('resize', resizeHandler);

      return () => {
        window.removeEventListener('resize', resizeHandler);
        myChart.dispose();
      };
    }
  }, [currency, data]);

  return <div ref={chartRef} style={{ width: '100%', height: '100%' }} />;
};

export default CampaignDetailChart;
