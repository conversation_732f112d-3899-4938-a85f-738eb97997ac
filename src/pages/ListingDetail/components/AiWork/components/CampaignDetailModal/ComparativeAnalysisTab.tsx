import React, { useState, useEffect } from 'react';
import { Card, Table, DatePicker, Typography, Space, Spin } from 'antd';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import { useModel } from '@umijs/max';
import {
  getCampaignSequentialAnalysis,
  getCampaignSynchronousAnalysis,
} from '@/services/ibidder_api/operation';
import type {
  SequentialAnalysisRecord,
  SynchronousAnalysisData,
} from './types';

const { RangePicker } = DatePicker;
const { Title } = Typography;

const sequentialAnalysisColumns = [
  { title: '时间周期', dataIndex: 'period', key: 'period' },
  { title: '广告订单', dataIndex: 'orders', key: 'orders' },
  { title: '广告花费', dataIndex: 'spend', key: 'spend' },
  { title: '广告销售额', dataIndex: 'sales', key: 'sales' },
  { title: 'ACoS', dataIndex: 'acos', key: 'acos' },
  { title: 'CVR', dataIndex: 'cvr', key: 'cvr' },
  { title: 'CTR', dataIndex: 'ctr', key: 'ctr' },
  { title: '曝光量', dataIndex: 'impressions', key: 'impressions' },
  { title: '点击', dataIndex: 'clicks', key: 'clicks' },
  { title: 'CPC', dataIndex: 'cpc', key: 'cpc' },
  { title: 'CPA', dataIndex: 'cpa', key: 'cpa' },
];

const synchronousAnalysisColumns = [
  { title: '日期', width: 160, fixed: 'left' as const, dataIndex: 'date', key: 'date' },
  { title: '预算', dataIndex: 'budget', key: 'budget' },
  { title: '广告花费', dataIndex: 'spend', key: 'spend' },
  { title: '曝光量', dataIndex: 'impressions', key: 'impressions' },
  { title: '点击', dataIndex: 'clicks', key: 'clicks' },
  { title: '广告订单', dataIndex: 'orders', key: 'orders' },
  { title: '销售数量', dataIndex: 'units', key: 'units' },
  { title: '广告销售额', dataIndex: 'sales', key: 'sales' },
  { title: 'SKU订单', dataIndex: 'sku_orders', key: 'sku_orders' },
  { title: 'SKU销售额', dataIndex: 'sku_sales', key: 'sku_sales' },
  { title: '搜索顶部展示份额', dataIndex: 'topOfSearchImpressionShare', key: 'topOfSearchImpressionShare' },
  { title: 'ACoS', dataIndex: 'acos', key: 'acos' },
  { title: 'CVR', dataIndex: 'cvr', key: 'cvr' },
  { title: 'CTR', dataIndex: 'ctr', key: 'ctr' },
  { title: 'CPC', dataIndex: 'cpc', key: 'cpc' },
  { title: 'CPA', dataIndex: 'cpa', key: 'cpa' },
];

interface ComparativeAnalysisTabProps {
  campaignId?: number;
  campaignType?: string;
}

const ComparativeAnalysisTab: React.FC<ComparativeAnalysisTabProps> = ({ campaignId ,campaignType}) => {
  const { productInfo } = useModel('productInfo');
  const profileId = productInfo?.profile_id;

  const [sequentialData, setSequentialData] = useState<SequentialAnalysisRecord[]>([]);
  const [synchronousData, setSynchronousData] = useState<SynchronousAnalysisData | null>(null);
  const [sequentialLoading, setSequentialLoading] = useState(false);
  const [synchronousLoading, setSynchronousLoading] = useState(false);

  useEffect(() => {
    if(!campaignId || !campaignType) return;
    const fetchSequentialData = async () => {
      if (!campaignId || !profileId) return;
      setSequentialLoading(true);
      try {
        const response = await getCampaignSequentialAnalysis({
          campaign_id: campaignId,
          campaign_type: campaignType,
          profile_id: profileId,
          start_date: dayjs().subtract(1, 'month').format('YYYY-MM-DD'),
          end_date: dayjs().format('YYYY-MM-DD'),
        });
        setSequentialData(response.data);
      } finally {
        setSequentialLoading(false);
      }
    };

    const fetchSynchronousData = async () => {
      if (!campaignId || !profileId) return;
      setSynchronousLoading(true);
      try {
        const response = await getCampaignSynchronousAnalysis({
          campaign_id: campaignId,
          campaign_type: campaignType,
          end_date: dayjs().format('YYYY-MM-DD'),
          profile_id: profileId,
          start_date: dayjs().subtract(1, 'month').format('YYYY-MM-DD'),
        });
        setSynchronousData(response.data);
      } finally {
        setSynchronousLoading(false);
      }
    };

    fetchSequentialData();
    fetchSynchronousData();
  }, [campaignId, profileId, campaignType]);

  if (!campaignId || !campaignType) return null;

  const handleSequentialDateChange = async (dates: null | (Dayjs | null)[], dateStrings: [string, string]) => {
    if (dates && campaignId && profileId) {
      setSequentialLoading(true);
      try {
        const response = await getCampaignSequentialAnalysis({
          campaign_id: campaignId,
          campaign_type: campaignType,
          profile_id: profileId,
          start_date: dateStrings[0],
          end_date: dateStrings[1],
        });
        setSequentialData(response.data);
      } finally {
        setSequentialLoading(false);
      }
    }
  };

  const handleSynchronousDateChange = async (dates: null | (Dayjs | null)[], dateStrings: [string, string]) => {
    if (dates && campaignId && profileId) {
      setSynchronousLoading(true);
      try {
        const response = await getCampaignSynchronousAnalysis({
          campaign_id: campaignId,
          campaign_type: campaignType,
          profile_id: profileId,
          start_date: dateStrings[0],
          end_date: dateStrings[1],
        });
        setSynchronousData(response.data);
      } finally {
        setSynchronousLoading(false);
      }
    }
  };

  return (
    <Spin spinning={sequentialLoading || synchronousLoading}>
      <div>
        <Title level={5}>
          <Space>
            <span>环比分析</span>
            <RangePicker onChange={handleSequentialDateChange} />
          </Space>
        </Title>
        <Card>
          <Table
            columns={sequentialAnalysisColumns}
            dataSource={sequentialData}
            pagination={false}
            loading={sequentialLoading}
            style={{ marginBottom: 24 }}
          />
        </Card>

        <Title level={5} style={{ marginTop: 32 }}>
          <Space>
            <span>同步分析</span>
            <RangePicker onChange={handleSynchronousDateChange} />
          </Space>
        </Title>
        <Card>
          <Table
            columns={synchronousAnalysisColumns}
            dataSource={synchronousData?.list || []}
            pagination={false}
            loading={synchronousLoading}
            scroll={{ x: 'max-content' }}
            summary={() => {
              if (!synchronousData?.total) return null;
              return (
                <Table.Summary fixed>
                  <Table.Summary.Row>
                    <Table.Summary.Cell index={0}>总计</Table.Summary.Cell>
                    <Table.Summary.Cell index={1}>{synchronousData.total.budget}</Table.Summary.Cell>
                    <Table.Summary.Cell index={2}>{synchronousData.total.spend}</Table.Summary.Cell>
                    <Table.Summary.Cell index={3}>{synchronousData.total.impressions}</Table.Summary.Cell>
                    <Table.Summary.Cell index={4}>{synchronousData.total.clicks}</Table.Summary.Cell>
                    <Table.Summary.Cell index={5}>{synchronousData.total.orders}</Table.Summary.Cell>
                    <Table.Summary.Cell index={6}>{synchronousData.total.units}</Table.Summary.Cell>
                    <Table.Summary.Cell index={7}>{synchronousData.total.sales}</Table.Summary.Cell>
                    <Table.Summary.Cell index={8}>{synchronousData.total.sku_orders}</Table.Summary.Cell>
                    <Table.Summary.Cell index={9}>{synchronousData.total.sku_sales}</Table.Summary.Cell>
                    <Table.Summary.Cell index={10}>{synchronousData.total.topOfSearchImpressionShare}</Table.Summary.Cell>
                    <Table.Summary.Cell index={11}>{synchronousData.total.acos}</Table.Summary.Cell>
                    <Table.Summary.Cell index={12}>{synchronousData.total.cvr}</Table.Summary.Cell>
                    <Table.Summary.Cell index={13}>{synchronousData.total.ctr}</Table.Summary.Cell>
                    <Table.Summary.Cell index={14}>{synchronousData.total.cpc}</Table.Summary.Cell>
                    <Table.Summary.Cell index={15}>{synchronousData.total.cpa}</Table.Summary.Cell>
                  </Table.Summary.Row>
                </Table.Summary>
              );
            }}
            rowKey="date"
          />
        </Card>
      </div>
    </Spin>
  );
};

export default ComparativeAnalysisTab;
