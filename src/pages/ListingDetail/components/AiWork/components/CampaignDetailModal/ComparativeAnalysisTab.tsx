import React, { useState, useEffect } from 'react';
import { Card, Table, DatePicker, Typography, Space, Spin } from 'antd';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import { useModel } from '@umijs/max';
import {
  getCampaignSequentialAnalysis,
  getCampaignSynchronousAnalysis,
} from '@/services/ibidder_api/operation';
import type {
  SequentialAnalysisRecord,
  SynchronousAnalysisRecord,
} from './types';

const { RangePicker } = DatePicker;
const { Title } = Typography;

const sequentialAnalysisColumns = [
  { title: '时间周期', dataIndex: 'period', key: 'period' },
  { title: '广告订单', dataIndex: 'orders', key: 'orders' },
  { title: '广告花费', dataIndex: 'spend', key: 'spend' },
  { title: '广告销售额', dataIndex: 'sales', key: 'sales' },
  { title: 'ACoS', dataIndex: 'acos', key: 'acos' },
  { title: 'CVR', dataIndex: 'cvr', key: 'cvr' },
  { title: 'CTR', dataIndex: 'ctr', key: 'ctr' },
  { title: '曝光量', dataIndex: 'impressions', key: 'impressions' },
  { title: '点击', dataIndex: 'clicks', key: 'clicks' },
  { title: 'CPC', dataIndex: 'cpc', key: 'cpc' },
  { title: 'CPA', dataIndex: 'cpa', key: 'cpa' },
];

const synchronousAnalysisColumns = [
  { title: '日期', dataIndex: 'date', key: 'date' },
  { title: '预算', dataIndex: 'budget', key: 'budget' },
  { title: '广告订单', dataIndex: 'orders', key: 'orders' },
  { title: '广告花费', dataIndex: 'spend', key: 'spend' },
  { title: '广告销售额', dataIndex: 'sales', key: 'sales' },
  { title: 'ACoS', dataIndex: 'acos', key: 'acos' },
  { title: 'CVR', dataIndex: 'cvr', key: 'cvr' },
  { title: 'CTR', dataIndex: 'ctr', key: 'ctr' },
  { title: '曝光量', dataIndex: 'impressions', key: 'impressions' },
  { title: '点击', dataIndex: 'clicks', key: 'clicks' },
  { title: 'CPC', dataIndex: 'cpc', key: 'cpc' },
];

interface ComparativeAnalysisTabProps {
  campaignId?: number;
  campaignType?: string;
}

const ComparativeAnalysisTab: React.FC<ComparativeAnalysisTabProps> = ({ campaignId ,campaignType}) => {
  const { productInfo } = useModel('productInfo');
  const profileId = productInfo?.profile_id;

  const [sequentialData, setSequentialData] = useState<SequentialAnalysisRecord[]>([]);
  const [synchronousData, setSynchronousData] = useState<SynchronousAnalysisRecord[]>([]);
  const [sequentialLoading, setSequentialLoading] = useState(false);
  const [synchronousLoading, setSynchronousLoading] = useState(false);

  useEffect(() => {
    if(!campaignId || !campaignType) return;
    const fetchSequentialData = async () => {
      if (!campaignId || !profileId) return;
      setSequentialLoading(true);
      try {
        const response = await getCampaignSequentialAnalysis({
          campaign_id: campaignId,
          profile_id: profileId,
          start_time: dayjs().subtract(2, 'month').format('YYYY-MM-DD'),
          end_time: dayjs().subtract(1, 'month').format('YYYY-MM-DD'),
        });
        setSequentialData(response.data);
      } finally {
        setSequentialLoading(false);
      }
    };

    const fetchSynchronousData = async () => {
      if (!campaignId || !profileId) return;
      setSynchronousLoading(true);
      try {
        const response = await getCampaignSynchronousAnalysis({
          campaign_id: campaignId,
          campaign_type: campaignType,
          end_date: dayjs().format('YYYY-MM-DD'),
          profile_id: profileId,
          start_date: dayjs().subtract(1, 'month').format('YYYY-MM-DD'),
        });
        setSynchronousData(response.data.list);
      } finally {
        setSynchronousLoading(false);
      }
    };

    fetchSequentialData();
    fetchSynchronousData();
  }, [campaignId, profileId, campaignType]);

  if (!campaignId || !campaignType) return null;

  const handleSequentialDateChange = async (dates: null | (Dayjs | null)[], dateStrings: [string, string]) => {
    if (dates && campaignId && profileId) {
      setSequentialLoading(true);
      try {
        const response = await getCampaignSequentialAnalysis({
          campaign_id: campaignId,
          profile_id: profileId,
          start_time: dateStrings[0],
          end_time: dateStrings[1],
        });
        setSequentialData(response.data);
      } finally {
        setSequentialLoading(false);
      }
    }
  };

  const handleSynchronousDateChange = async (dates: null | (Dayjs | null)[], dateStrings: [string, string]) => {
    if (dates && campaignId && profileId) {
      setSynchronousLoading(true);
      try {
        const response = await getCampaignSynchronousAnalysis({
          campaign_id: campaignId,
          campaign_type: campaignType,
          profile_id: profileId,
          start_date: dateStrings[0],
          end_date: dateStrings[1],
        });
        setSynchronousData(response.data.list);
      } finally {
        setSynchronousLoading(false);
      }
    }
  };

  return (
    <Spin spinning={sequentialLoading || synchronousLoading}>
      <div>
        <Title level={5}>
          <Space>
            <span>环比分析</span>
            <RangePicker onChange={handleSequentialDateChange} />
          </Space>
        </Title>
        <Card>
          <Table
            columns={sequentialAnalysisColumns}
            dataSource={sequentialData}
            pagination={false}
            loading={sequentialLoading}
            style={{ marginBottom: 24 }}
          />
        </Card>

        <Title level={5} style={{ marginTop: 32 }}>
          <Space>
            <span>同步分析</span>
            <RangePicker onChange={handleSynchronousDateChange} />
          </Space>
        </Title>
        <Card>
          <Table
            columns={synchronousAnalysisColumns}
            dataSource={synchronousData}
            pagination={false}
            loading={synchronousLoading}
          />
        </Card>
      </div>
    </Spin>
  );
};

export default ComparativeAnalysisTab;
