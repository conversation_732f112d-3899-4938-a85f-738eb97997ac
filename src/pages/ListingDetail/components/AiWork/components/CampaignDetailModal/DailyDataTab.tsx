import React, { useState, useEffect, useMemo } from 'react';
import { Card, Table, DatePicker, Button, Space, Row, Col, Spin } from 'antd';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import { useModel } from '@umijs/max';
import CampaignDetailChart from './CampaignDetailChart';
import { getCampaignDailyData } from '@/services/ibidder_api/operation';
import type { DailyChartData, DailyTableRecord, DailyDataTabProps } from './types';

const { RangePicker } = DatePicker;

const dailyTableColumns = [
  { title: '日期', width: 120, fixed: 'left' as const, dataIndex: 'date', key: 'date' },
  { title: '预算', dataIndex: 'budget', key: 'budget' },
  { title: '广告订单', dataIndex: 'orders', key: 'orders' },
  { title: '广告花费', dataIndex: 'spend', key: 'spend' },
  { title: '广告销售额', dataIndex: 'sales', key: 'sales' },
  { title: 'ACoS', dataIndex: 'acos', key: 'acos' },
  { title: 'CVR', dataIndex: 'cvr', key: 'cvr' },
  { title: 'CTR', dataIndex: 'ctr', key: 'ctr' },
  { title: '曝光量', dataIndex: 'impressions', key: 'impressions' },
  { title: '点击', dataIndex: 'clicks', key: 'clicks' },
  { title: 'CPC', dataIndex: 'cpc', key: 'cpc' },
];

const DailyDataTab: React.FC<DailyDataTabProps> = ({ campaignId }) => {
  const { productInfo } = useModel('productInfo');
  const profileId = productInfo?.profile_id;
  const currency = productInfo?.currency || '$';

  const [tableData, setTableData] = useState<DailyTableRecord[]>([]);
  const [loading, setLoading] = useState(false);
  const [granularity, setGranularity] = useState<'day' | 'week' | 'month'>('day');
  const [dateRange, setDateRange] = useState<[string, string]>([
    dayjs().subtract(1, 'month').format('YYYY-MM-DD'),
    dayjs().format('YYYY-MM-DD'),
  ]);

  useEffect(() => {
    const fetchData = async () => {
      if (!campaignId || !profileId) {
        console.log('[Debug] Aborting fetch: campaignId or profileId is missing.', { campaignId, profileId });
        return;
      }

      setLoading(true);
      try {
        const response = await getCampaignDailyData({
          campaign_id: campaignId,
          profile_id: profileId,
          start_time: dateRange[0],
          end_time: dateRange[1],
          granularity: granularity,
        });
        setTableData(response.data);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [campaignId, profileId, dateRange, granularity]);

  const chartData: DailyChartData | null = useMemo(() => {
    if (!tableData || tableData.length === 0) {
      return null;
    }
    const reversedData = [...tableData].reverse();
    return {
      dates: reversedData.map((d) => d.date),
      spend: reversedData.map((d) => d.spend),
      sales: reversedData.map((d) => d.sales),
      acos: reversedData.map((d) => parseFloat(d.acos)),
      cvr: reversedData.map((d) => parseFloat(d.cvr)),
      ctr: reversedData.map((d) => parseFloat(d.ctr)),
      impressions: reversedData.map((d) => d.impressions),
      clicks: reversedData.map((d) => d.clicks),
      cpc: reversedData.map((d) => d.cpc),
    };
  }, [tableData]);

  const handleDateChange = (
    dates: null | (Dayjs | null)[] | any,
    dateStrings: [string, string],
  ) => {
    if (dates && dateStrings[0] && dateStrings[1]) {
      setDateRange(dateStrings);
    }
  };

  const handleGranularityChange = (gran: 'day' | 'week' | 'month') => {
    setGranularity(gran);
  };

  return (
    <Spin spinning={loading}>
      <div>
        <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
          <Col>
            <Space>
              <RangePicker
                defaultValue={[dayjs(dateRange[0]), dayjs(dateRange[1])]}
                onChange={handleDateChange}
              />
              <Space.Compact>
                <Button
                  type={granularity === 'day' ? 'primary' : 'default'}
                  onClick={() => handleGranularityChange('day')}
                >
                  天
                </Button>
                <Button
                  type={granularity === 'week' ? 'primary' : 'default'}
                  onClick={() => handleGranularityChange('week')}
                >
                  周
                </Button>
                <Button
                  type={granularity === 'month' ? 'primary' : 'default'}
                  onClick={() => handleGranularityChange('month')}
                >
                  月
                </Button>
              </Space.Compact>
            </Space>
          </Col>
        </Row>

        <Card>
          <div style={{ height: '300px' }}>
            {chartData && (
              <CampaignDetailChart currency={currency} data={chartData as any} />
            )}
          </div>
        </Card>

        <Card style={{ marginTop: 32 }}>
          <Table
            columns={dailyTableColumns}
            dataSource={tableData}
            pagination={{ pageSize: 5 }}
            scroll={{ x: 'max-content' }}
          />
        </Card>
      </div>
    </Spin>
  );
};

export default DailyDataTab;