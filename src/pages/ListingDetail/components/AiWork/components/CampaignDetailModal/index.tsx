import React from 'react';
import { Modal, Typography, Tabs, Space } from 'antd';
import type { TabsProps } from 'antd';
import type { CampaignDetailModalProps } from './types';
import DailyDataTab from './DailyDataTab';
import ComparativeAnalysisTab from './ComparativeAnalysisTab';

const { Title, Text } = Typography;

const CampaignDetailModal: React.FC<CampaignDetailModalProps> = ({ visible, onCancel, campaignData }) => {
  console.log(8989,campaignData)
  if (!campaignData) {
    return null;
  }

  const items: TabsProps['items'] = [
    {
      key: '1',
      label: '天数据',
      children: <DailyDataTab campaignId={campaignData.campaign_id} />,
    },
    {
      key: '2',
      label: '对比分析',
      children: <ComparativeAnalysisTab campaignId={campaignData.campaign_id} campaignType={campaignData.campaign_type} />,
    },
  ];

  return (
    <Modal
      title={
        <Title level={4} style={{ margin: 0 }}>
          <Space>
            <span>广告活动详情</span>
            <Text type="secondary">{campaignData.campaign_type}</Text>
          </Space>
        </Title>
      }
      open={visible}
      onCancel={onCancel}
      footer={null}
      width={1200}
      destroyOnClose
    >
      <Tabs defaultActiveKey="1" items={items} />
    </Modal>
  );
};

export default CampaignDetailModal;