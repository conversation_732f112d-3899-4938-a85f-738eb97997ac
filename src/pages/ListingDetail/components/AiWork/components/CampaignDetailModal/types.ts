export interface CampaignDetailModalProps {
  visible: boolean;
  onCancel: () => void;
  campaignData: {
    campaign_id?: number;
    campaign_type?: string;
  };
}

export interface DailyDataTabProps {
  campaignId?: number;
}

// API Types

export interface GetCampaignDailyDataParams {
  profile_id: number;
  campaign_id: number;
  start_time: string;
  end_time: string;
  granularity: 'day' | 'week' | 'month';
}

export interface GetCampaignSequentialAnalysisParams {
  campaign_id: number;
  campaign_type: string;
  end_date: string;
  profile_id: number;
  start_date: string;
}

export interface GetCampaignSynchronousAnalysisParams {
  campaign_id: number;
  campaign_type: string;
  end_date: string;
  profile_id: number;
  start_date: string;
}

// Data Record Types

export interface DailyTableRecord {
  date: string;
  budget: number;
  orders: number;
  spend: number;
  sales: number;
  acos: string;
  cvr: string;
  ctr: string;
  impressions: number;
  clicks: number;
  cpc: number;
}

export interface DailyChartData {
  dates: string[];
  spend: number[];
  sales: number[];
  acos: number[];
  cvr: number[];
  ctr: number[];
  impressions: number[];
  clicks: number[];
  cpc: number[];
}

export interface SequentialAnalysisRecord {
  period: string;
  orders: number | string;
  spend: number | string;
  sales: number | string;
  acos: string;
  cvr: string;
  ctr: string;
  impressions: number | string;
  clicks: number | string;
  cpc: number | string;
  cpa: number | string;
}

export interface SequentialAnalysisData {
  current_period: {
    period: string;
    orders: number;
    spend: number;
    sales: number;
    acos: string;
    cvr: string;
    ctr: string;
    impressions: number;
    clicks: number;
    cpc: number;
    cpa: number;
  };
  previous_period: {
    period: string;
    orders: number;
    spend: number;
    sales: number;
    acos: string;
    cvr: string;
    ctr: string;
    impressions: number;
    clicks: number;
    cpc: number;
    cpa: number;
  };
  change_period: {
    period: string;
    orders: number;
    spend: number;
    sales: number;
    acos: string;
    cvr: string;
    ctr: string;
    impressions: number;
    clicks: number;
    cpc: number;
    cpa: number;
  };
  change_rate: {
    period: string;
    orders: string;
    spend: string;
    sales: string;
    acos: string;
    cvr: string;
    ctr: string;
    impressions: string;
    clicks: string;
    cpc: string;
    cpa: string;
  };
}

export interface SynchronousAnalysisRecord {
  date: string;
  budget: number;
  spend: number;
  impressions: number;
  clicks: number;
  orders: number;
  units: number;
  sales: number;
  sku_orders: number;
  sku_sales: number;
  topOfSearchImpressionShare: number;
  acos: string;
  cvr: string;
  ctr: string;
  cpc: string;
  cpa: string;
  children?: SynchronousAnalysisChildRecord[];
}

export interface SynchronousAnalysisChildRecord {
  date: string;
  weekday: string;
  budget: number;
  impressions: number;
  clicks: number;
  orders: number;
  units: number;
  sales: number;
  spend: number;
  campaignType: string;
  campaignId: number;
  topOfSearchImpressionShare: number;
  sku_orders: number;
  sku_sales: number;
  acos: string;
  cvr: string;
  ctr: string;
  cpc: string;
  cpa: string;
}

export interface SynchronousAnalysisData {
  list: SynchronousAnalysisRecord[];
  total: {
    budget: number;
    spend: number;
    impressions: number;
    clicks: number;
    orders: number;
    units: number;
    sales: number;
    sku_orders: number;
    sku_sales: number;
    topOfSearchImpressionShare: number;
    acos: string;
    cvr: string;
    ctr: string;
    cpc: string;
    cpa: string;
  };
}
