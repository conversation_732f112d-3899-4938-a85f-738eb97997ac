import React from 'react';
import { Card, Table, Typography } from 'antd';
const { Title } = Typography;

interface RevisionHistoryProps {
  revision_history?: Strategy.Revision_history;
}

const RevisionHistory: React.FC<RevisionHistoryProps> = ({ revision_history }) => {
  // 如果没有修订记录数据，不渲染组件
  if (!revision_history || Object.keys(revision_history).length === 0) {
    return null;
  }

  // 将修订记录对象转换为表格数据
  const dataSource = Object.entries(revision_history).map(([time, record], index) => ({
    key: index,
    version: `版本${index + 1}`,
    time: time,
    user: record.user_name,
    content: record.revision_content.join('；')
  }));

  // 表格列配置
  const columns = [
    {
      title: '版本号',
      dataIndex: 'version',
      key: 'version',
      width: 100
    },
    {
      title: '修改时间',
      dataIndex: 'time',
      key: 'time',
      width: 220
    },
    {
      title: '修改人',
      dataIndex: 'user',
      key: 'user',
      width: 220
    },
    {
      title: '修改内容',
      dataIndex: 'content',
      key: 'content'
    }
  ];

  return (
    <>
      <Title level={3} style={{ marginTop: "48px" }}>修订记录</Title>
      <Card data-test-id="revision-history" className='card'>
        <Table
          size='large'
          dataSource={dataSource}
          columns={columns}
          pagination={false}
        />
      </Card>
    </>
  );
};

export default RevisionHistory;