import React, { useState, useEffect } from 'react';
import { getAvatarByType } from '../bus';
import { Card, Divider, Input, Table, Switch, Button, Dropdown, Checkbox, Pagination, Tooltip, message, Tag } from 'antd';
import { SyncOutlined, QuestionCircleOutlined, DownOutlined } from '@ant-design/icons';
import { useSearchParams, useModel } from '@umijs/max';
import styles from './style.less';
import { getCampaignList, batchToggleCampaign } from '@/services/ibidder_api/ads';
import { syncListingCampaign } from '@/services/ibidder_api/listings';

const { Search } = Input;

interface CampaignData {
  campaign_id: string;
  campaign_type: string;
  campaign_name: string;
  acos_14_days: number;
  acos_historical: number;
  budget: number;
  ai_managed: boolean;
}
const CampaignManage: React.FC = () => {
  const [cardLoading, setCardLoading] = useState(false);
  const { productInfo } = useModel('productInfo');
  const currency = productInfo?.currency || '$';
  const [searchParams] = useSearchParams();
  const parentAsin = searchParams.get('asin') as string;
  const profileId = searchParams.get('profile_id') as string;
  
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [searchText, setSearchText] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [campaignData, setCampaignData] = useState<CampaignData[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);

  const columns = [
    {
      title: '',
      dataIndex: 'selection',
      key: 'selection',
      width: 30,
    },
    {
      title: '类型',
      dataIndex: 'campaign_type',
      key: 'campaign_type',
      width: 80,
      render: (text: string) => {
        if (text === null || text === undefined) return '-';
        let color = 'blue';
        if (text === 'sb') {
          color = 'red';
        } else if (text === 'sd') {
          color = 'green';
        }
        return (
          <Tag color={color}>{text.toUpperCase()}</Tag>
        );
      }
    },
    {
      title: '广告活动',
      dataIndex: 'campaign_name',
      key: 'campaign_name',
      ellipsis: true,
    },
    {
      title: '近14天ACOS',
      dataIndex: 'acos_14_days',
      key: 'acos_14_days',
      width: 130,
      render: (value: number) => {
        if (value === null || value === undefined) return '-';
        return `${value}%`;
      }
    },
    {
      title: '历史同期ACOS',
      dataIndex: 'acos_historical',
      key: 'acos_historical',
      width: 130,
      render: (value: number) => {
        if (value === null || value === undefined) return '-';
        return `${value}%`;
      }
    },
    {
      title: '预算',
      dataIndex: 'budget',
      key: 'budget',
      width: 120,
      render: (value: number) => {
        if (value === null || value === undefined) return '-';
        return `${currency}${Number(value).toFixed(2)}`;
      }
    },
    {
      title: (
        <span>
          AI管理状态
          <Tooltip title="AI的预算分配和竞价调整功能仅适用于您【已启用】的广告活动。【已暂停】的广告活动将不会受到AI的管理或干预。请放心，AI不会自行暂停您的亚马逊后台广告活动。">
            <QuestionCircleOutlined style={{ marginLeft: 4, color: '#999' }} />
          </Tooltip>
        </span>
      ),
      dataIndex: 'ai_managed',
      key: 'ai_managed',
      width: 120,
      render: (enabled: boolean, record: CampaignData) => (
        <Switch
          style={{ width: 70 }}
          checkedChildren="已启用"
          unCheckedChildren="已暂停"
          checked={enabled}
          onChange={(checked) => handleAiManagementChange(checked, record)}
        />
      ),
    },
  ];

  const batchOperations = [
    { key: 'enable', label: '启用' },
    { key: 'disable', label: '暂停' },
  ];

  // 获取广告活动列表数据
  const fetchCampaignData = (page: number, size: number, search?: string) => {
    setLoading(true);
    return getCampaignList({
      parent_asin: parentAsin,
      profile_id: parseInt(profileId),
      campaign_name: search,
      page: page,
      page_size: size,
    }).then(response => {
      return response;
    }).catch(error => {
      message.error('获取数据失败');
      throw error;
    }).finally(() => {
      setLoading(false);
      setCardLoading(false);
    });
  };

  // 加载数据
  const loadData = (page: number, size: number, search?: string) => {
    fetchCampaignData(page, size, search).then(response => {
      const responseData = response.data;
      setCampaignData(responseData.campaigns);
      // 前端直接管理分页参数，不依赖接口返回
      setTotal(responseData.total || 0);
      setCurrentPage(page);
      setPageSize(size);
    }).catch(error => {
      console.error('加载数据失败:', error);
    });
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedRowKeys(campaignData.map(item => item.campaign_id + '-' + item.campaign_type));
    } else {
      setSelectedRowKeys([]);
    }
  };

  // 处理分页变化
  const handlePageChange = (page: number, size?: number) => {
    const newPageSize = size || pageSize;
    setSelectedRowKeys([]); // 清空选择
    loadData(page, newPageSize, searchText);
  };

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchText(value.trim());
    setCurrentPage(1);
    loadData(1, pageSize, value.trim());
  };

  // 处理批量操作
  const handleBatchOperation = (operation: string) => {
    if (selectedRowKeys.length === 0) {
      message.error('请先选择要操作的广告活动');
      return;
    }
    batchToggleCampaign({
      parent_asin: parentAsin,
      profile_id: parseInt(profileId),
      campaign_list: selectedRowKeys.map(key => {
        const [campaign_id, campaign_type] = key.split('-');
        return { campaign_id, campaign_type };
      }),
      enabled: operation === 'enable',
    }).then(res => {
      if (res.code === 200) {
        message.success('操作成功');
        setSelectedRowKeys([]);
        loadData(currentPage, pageSize, searchText);
      } else {
        message.error(res.message);
      }
    });
  };

  // 处理AI管理状态更新
  const handleAiManagementChange = (checked: boolean, record: CampaignData) => {
    batchToggleCampaign({
      parent_asin: parentAsin,
      profile_id: parseInt(profileId),
      campaign_list: [{ campaign_id: record.campaign_id, campaign_type: record.campaign_type }],
      enabled: checked,
    }).then(res => {
      if (res.code === 200) {
        message.success('操作成功');
        setCampaignData(prev => prev.map(item => item.campaign_id === record.campaign_id ? { ...item, ai_managed: checked } : item)); 
      } else {
        message.error(res.message);
      }
    });
  };

  const handleSync = () => {
    if (loading) return;
    setLoading(true);
    syncListingCampaign({
      parent_asin: parentAsin,
      profile_id: parseInt(profileId),
    }).then(res => {
      if (res.code === 200) {
        message.success('同步成功');
        setCurrentPage(1);
        loadData(1, pageSize, searchText);
      } else {
        message.error(res.message);
      }
    }).finally(() => {
      setLoading(false);
    });
  };

  // 初始加载
  useEffect(() => {
    if (parentAsin && profileId) {
      setCardLoading(true);
      loadData(currentPage, pageSize);
    }
  }, [parentAsin, profileId]);

  return (
    <>
      <Card data-test-id="campaign-manage" className="card" loading={cardLoading}>
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16, marginTop: 16 }}>
          {getAvatarByType('strategyAgent')}
          <div className={styles.aiWorkRole}>
            <span className={styles.aiWorkType}>广告策略师</span>
            <span className={styles.aiWorkDesc}>广告增长引擎</span>
          </div>
          <Divider type="vertical" style={{ height: 48 }} />
          <div style={{ marginLeft: 24, marginRight: 16, color: '#000000' }}>Campaign管理：</div>
          <div style={{ color: '#979797', marginLeft: 16 }}>
            <span style={{ color: '#D80027' }}>*</span>
            AI将按照您设置的广告活动管理状态，对开启状态的广告活动进行预算分配和竞价调整
          </div>
        </div>
        <div className={styles.campaignManage}>
          <div className={styles.content}>
            <div className={styles.toolbar}>
              <div className={styles.leftTools}>
                <Search
                  placeholder="查找广告活动"
                  allowClear
                  style={{ width: 300 }}
                  value={searchText}
                  onSearch={handleSearch}
                  onChange={(e) => setSearchText(e.target.value)}
                />
                <Dropdown
                  menu={{
                    items: batchOperations,
                    onClick: ({ key }) => {
                      handleBatchOperation(key);
                    },
                  }}
                >
                  <Button>
                    批量操作 <DownOutlined />
                  </Button>
                </Dropdown>
              </div>
              <Tooltip
                title="可同步该Listing下最新已开启的广告活动列表"
                placement="top"
                overlayInnerStyle={{
                  width: 180,
                }}
              >
                <Button 
                  icon={<SyncOutlined />}
                  onClick={handleSync}
                  className={styles.syncButton}
                >
                  同步
                </Button>
              </Tooltip>
            </div>

            <div className={styles.tableContainer}>
              <Table
                rowKey={record => record.campaign_id + '-' + record.campaign_type}
                columns={columns}
                dataSource={campaignData}
                pagination={false}
                loading={loading}
                rowSelection={{
                  selectedRowKeys,
                  onChange: (keys) => setSelectedRowKeys(keys as string[]),
                }}
                className={styles.campaignTable}
              />
              
              <div className={styles.tableFooter}>
                <div className={styles.selectionInfo}>
                  <Checkbox
                    checked={selectedRowKeys.length === campaignData.length}
                    indeterminate={selectedRowKeys.length > 0 && selectedRowKeys.length < campaignData.length}
                    onChange={(e) => handleSelectAll(e.target.checked)}
                  >
                    全选
                  </Checkbox>
                  <span className={styles.selectedCount}>
                    选中{selectedRowKeys.length}条
                  </span>
                </div>
                <Pagination
                  current={currentPage}
                  total={total}
                  pageSize={pageSize}
                  showSizeChanger={true}
                  showQuickJumper={false}
                  onChange={handlePageChange}
                  onShowSizeChange={handlePageChange}
                  pageSizeOptions={['10', '20', '50', '100']}
                  showTotal={(total) => `共 ${total} 条`}
                />
              </div>
            </div>
          </div>
        </div>
      </Card>
    </>
  );
};

export default CampaignManage;
