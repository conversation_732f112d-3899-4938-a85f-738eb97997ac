.aiWorkRole {
  height: 48px;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  margin-right: 16px;
}
.aiWorkDesc {
  font-size: 12px;
  color: #8c8c8c;
}

.aiWorkType {
  font-weight: bold;
  color: #333;
  white-space: nowrap;
  width: 100px;
}
.campaignManage {
  background-color: #F9FAFB;
  padding: 20px;
  
  .content {
    border-radius: 8px;
    background: #fff;
    padding: 12px 20px;
    
    .toolbar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
      
      .leftTools {
        display: flex;
        gap: 12px;
        align-items: center;
      }
    }
    
    .tableContainer {
      .campaignTable {
        .ant-table-thead > tr > th {
          background: #fafafa;
          border-bottom: 1px solid #f0f0f0;
          font-weight: 500;
        }
        
        .ant-table-tbody > tr > td {
          border-bottom: 1px solid #f0f0f0;
        }
        
        .ant-table-tbody > tr:hover > td {
          background: #f5f5f5;
        }
      }
      
      .tableFooter {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 0;
        
        .selectionInfo {
          display: flex;
          align-items: center;
          gap: 12px;
          
          .selectedCount {
            font-size: 14px;
            color: #0C1D23;
          }
        }
      }
    }
  }
}