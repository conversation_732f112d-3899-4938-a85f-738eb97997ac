import React from 'react';
import { Card, Row, Col, Statistic, Typography, Flex } from 'antd';
import { processNumberOrString } from '@/utils/bus';
import { useModel } from 'umi';

const { Title, Text } = Typography;

const DayStrategyCard = (props: {
  dayStrategyData: any,
  revision_history?: Strategy.Revision_history;
}) => {
  const { dayStrategyData } = props;
  const { productInfo } = useModel('productInfo');
  const currency = productInfo?.currency || '$';
  return (
    <>
      <Row gutter={16}>
        <Col span={12}>
          <Card data-test-id="day-strategy-card-overall-strategy" className="card" style={{ marginBottom: 0 }}>
            <Flex justify="space-between" align="center">
              <Title level={4} style={{ marginBottom: 0 }}>总体策略</Title>
            </Flex>
            <div style={{ height: '64px', flex: '1 0 auto', display: 'flex', flexDirection: 'column', justifyContent: 'center', marginTop: 8 }}>
              <div
                style={{ fontSize: '2em', fontWeight: 'bold', color: dayStrategyData.approach === 'balanced' ? '#1890ff' : dayStrategyData.approach === 'aggressive' ? '#f5222d' : dayStrategyData.approach === 'conservative' ? '#52c41a' : '#1890ff' }}>
                {dayStrategyData.approach === 'balanced' ? '平衡' :
                  dayStrategyData.approach === 'aggressive' ? '激进' :
                    dayStrategyData.approach === 'conservative' ? '保守' :
                      dayStrategyData.approach}
              </div>
            </div>
            <div style={{ marginTop: 'auto' }}>
              <Text style={{ fontSize: '14px', color: '#666' }}>{dayStrategyData.rationale}</Text>
            </div>

          </Card>
        </Col>
        <Col span={12}>
          <Card data-test-id="day-strategy-card-day-budget" className="card" style={{ marginBottom: 0 }}>
            <Flex justify="space-between" align="center">
              <Title level={4} style={{ marginBottom: 0 }}>今日预算</Title>
            </Flex>
            <div style={{ height: '64px', display: 'flex', alignItems: 'center', gap: '24px', marginTop: 8 }}>
              <Statistic
                value={dayStrategyData.day_budget.amount}
                prefix={currency}
                precision={2}
                valueStyle={{ color: '#333', fontSize: '32px', fontWeight: 'bold' }}
              />
              {dayStrategyData.day_budget.yesterday && (
                <>
                  <Text style={{ fontSize: '14px', color: '#999', display: 'block', marginTop: 4 }}>
                    昨日：{currency}{dayStrategyData.day_budget.yesterday}
                  </Text>
                  <Text style={{ fontSize: '14px', color: '#f5222d', display: 'block', marginTop: 4 }}>
                    {processNumberOrString(dayStrategyData.day_budget.change_from_yesterday, '%')}
                  </Text>
                </>
              )}
              {dayStrategyData.day_budget.adjustment_range && (
                <Text style={{ fontSize: '14px', color: '#999', display: 'block', marginTop: 4 }}>
                  调整范围：{currency}{dayStrategyData.day_budget.adjustment_range.min} ~ {currency}{dayStrategyData.day_budget.adjustment_range.max}
                </Text>
              )}
            </div>

            <div style={{ marginTop: 'auto' }}>
              <Text style={{ fontSize: '14px', color: '#666', display: 'block' }}>
                {dayStrategyData.day_budget.rationale}
              </Text>
            </div>

          </Card>
        </Col>
      </Row>
      <Card data-test-id="day-strategy-card-bid-adjustment" className="card" style={{ display: 'flex', flexDirection: 'column', marginTop: "16px" }}>
        <Flex justify="space-between" align="center">
          <Title level={4} style={{ marginBottom: 16 }}>竞价调整</Title>
        </Flex>
        <Row gutter={[24, 24]} style={{ flex: 1 }}>
          <Col span={10}>
            <Statistic
              title={<div style={{ fontSize: '14px', color: '#8c8c8c', marginBottom: 8 }}>建议调整</div>}
              value={dayStrategyData.bid_adjustment_range.min * 100}
              suffix="%"
              precision={0}
              valueStyle={{
                fontSize: '24px',
                fontWeight: 'bold',
                color: '#333'
              }}
            />
          </Col>
          <Col span={14}>
            <Statistic
              title={<div style={{ fontSize: '14px', color: '#8c8c8c', marginBottom: 8 }}>调整范围</div>}
              value={`${(dayStrategyData.bid_adjustment_range.min * 100).toFixed(0)}% ~ ${(dayStrategyData.bid_adjustment_range.max * 100).toFixed(0)}%`}
              valueStyle={{
                fontSize: '24px',
                fontWeight: 'bold',
                color: '#333'
              }}
            />
            <Text style={{ fontSize: '14px', color: '#8c8c8c', display: 'block', marginTop: 4 }}>
              AI 会根据实际情况在调整范围内动态调整
            </Text>
          </Col>
        </Row>
      </Card>
    </>
  )
}

export default DayStrategyCard;