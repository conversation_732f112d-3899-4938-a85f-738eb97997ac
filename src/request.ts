import { request, type RequestOptions } from '@umijs/max';
import type { AxiosError } from 'axios';
import AuthUtils from './utils/auth';

interface RefreshTokenData {
  access_token: string;
  access_expires?: number;
  refresh_token?: string;
  refresh_expires?: number;
}

interface RefreshApiResponse {
  code: number;
  data: RefreshTokenData;
}

interface ApiResponse<T> {
  code: number;
  data: T;
  message: string;
}

interface ITokenManager {
  getAccessToken: () => string | null;
  getAccessTokenExpires: () => string | null;
  getRefreshToken: () => string | null;
  setAccessToken: (token: string) => void;
  setAccessTokenExpires: (expiresInSeconds: number) => void;
  clearAccessToken: () => void;
  setRefreshToken: (token: string) => void;
  setRefreshTokenExpires: (expiresInSeconds: number) => void;
  getRefreshTokenExpires: () => number | null;
  isRefreshTokenExpired: () => boolean;
  clearAllTokensAndUserInfo: () => void;
}

// Token管理工具
const TokenManager: ITokenManager = {
  // 获取access_token
  getAccessToken: () => {
    return localStorage.getItem('access_token');
  },

  // 获取access_token过期时间
  getAccessTokenExpires: () => {
    return localStorage.getItem('access_token_expires');
  },

  // 获取refresh_token
  getRefreshToken: () => {
    return localStorage.getItem('refresh_token');
  },

  // 设置access_token
  setAccessToken: (token: string) => {
    localStorage.setItem('access_token', token);
  },

  // 设置access_token过期时间
  setAccessTokenExpires: (expiresInSeconds: number) => {
    const expiresAt = Date.now() + (expiresInSeconds * 1000);
    localStorage.setItem('access_token_expires', expiresAt.toString());
  },

  // 清除access_token
  clearAccessToken: () => {
    localStorage.removeItem('access_token');
    localStorage.removeItem('access_token_expires');
  },

  // 设置refresh_token到HttpOnly Cookie
  setRefreshToken: (token: string) => {
    localStorage.setItem('refresh_token', token);
  },

  // 设置refresh_token过期时间
  setRefreshTokenExpires: (expiresInSeconds: number) => {
    const expiresAt = Date.now() + (expiresInSeconds * 1000);
    localStorage.setItem('refresh_token_expires', expiresAt.toString());
  },

  // 获取refresh_token过期时间
  getRefreshTokenExpires: () => {
    const expires = localStorage.getItem('refresh_token_expires');
    return expires ? parseInt(expires, 10) : null;
  },

  // 检查refresh_token是否过期
  isRefreshTokenExpired: () => {
    const expires = TokenManager.getRefreshTokenExpires();
    if (!expires) {
      return true;
    }
    const isExpired = Date.now() > expires;
    return isExpired;
  },

  // 清除所有tokens
  clearAllTokensAndUserInfo: () => {
    localStorage.removeItem('access_token');
    localStorage.removeItem('access_token_expires');
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('refresh_token_expires');
    localStorage.removeItem('user_info');
  }
};

/* 辅助方法：尝试刷新token */
export const _performTokenRefreshAttempt = async (): Promise<string | null> => {
  try {
    // 调用刷新token的API - 使用原始的request方法，避免循环依赖
    const response = await fetch('/api/v1/auth/refresh', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        refresh_token: TokenManager.getRefreshToken(),
      }),
    });
    const data: RefreshApiResponse = await response.json();

    if (data.code === 200) {
      const result = data.data
      if (result?.access_token) {
        TokenManager.setAccessToken(result.access_token);

        if (result.access_expires) {
          TokenManager.setAccessTokenExpires(result.access_expires);
        }
        // 如果返回了新的refresh_token和过期时间，更新它们
        if (result.refresh_token) {
          TokenManager.setRefreshToken(result.refresh_token);
        }
        if (result.refresh_expires) {
          TokenManager.setRefreshTokenExpires(result.refresh_expires);
        }
        return result.access_token;
      }
    }
    AuthUtils.logout();
    return null;
  } catch (error) {
    console.error('刷新token失败:', error);
    AuthUtils.logout();
    return null;
  }
};

/*  刷新token的方法 */
const refreshToken = async (): Promise<string | null> => {
  // 检查refresh_token是否过期
  if (TokenManager.isRefreshTokenExpired()) {
    AuthUtils.logout();
    return null;
  }
  return _performTokenRefreshAttempt();
};

// 请求拦截器 - 添加认证头
const authInterceptor = (url: string, options: RequestOptions) => {
  const accessToken = TokenManager.getAccessToken();

  const headers = {
    ...options.headers,
  };

  // 如果有access_token，添加到Authorization头
  if (accessToken) {
    headers['Authorization'] = `bearer ${accessToken}`;
  }

  return {
    url,
    options: { ...options, headers },
  };
};

// 使用默认拦截器扩展原始请求
const extendedRequest = async <T = unknown>(
  url: string,
  options: RequestOptions = {},
): Promise<ApiResponse<T>> => {
  const enrichedOptions = {
    ...options,
    requestInterceptors: [authInterceptor, ...(options.requestInterceptors || [])],
  };

  try {
    return await request<ApiResponse<T>>(url, enrichedOptions);
  } catch (error) {
    const axiosError = error as AxiosError;
    if (axiosError?.response?.status === 403) {
      const newToken = await refreshToken();
      if (newToken) {
        // Token refreshed, retry the original request.
        // The authInterceptor will automatically use the new token.
        return request<ApiResponse<T>>(url, enrichedOptions);
      }
    }
    // For any other error, or if token refresh fails, re-throw.
    throw error;
  }
};

export { extendedRequest as request, TokenManager };