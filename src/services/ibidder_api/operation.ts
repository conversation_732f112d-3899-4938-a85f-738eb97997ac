// @ts-ignore
/* eslint-disable */
// API 更新时间：
// API 唯一标识：

import { request } from '@/request';
import type {
  DailyTableRecord,
  GetCampaignDailyDataParams,
  GetCampaignSequentialAnalysisParams,
  GetCampaignSynchronousAnalysisParams,
  SequentialAnalysisData,
  SynchronousAnalysisData,
} from '@/pages/ListingDetail/components/AiWork/components/CampaignDetailModal/types';


/**
 * 获取AI工作动态
 */
export async function getAIWorkHistory<T>(params: API.GetAIWorkHistoryParams) {
  return request<API.TableList<T>>('/api/v1/log/roleAgent/list', {
    method: 'POST',
    data: params,
  });
}

/**
 * 获取策略
 */
export async function getAdStrategy<T>(params: API.GetAdStrategyParams) {
  return request<T>('/api/v1/log/roleAgent/adStrategy', {
    method: 'GET',
    params,
  });
}

/**
 * 更新点赞和踩反馈
 */
export async function updateReport(params: any) {
  return request('/api/v1/log/roleAgent/updateReport', {
    method: 'POST',
    data: params,
  });
}

/**
 * 获取根据的策略或者市场报告
 */
export async function getBaseReport(params: API.GetBaseReportParams) {
  return request('/api/v1/log/roleAgent/target_report', {
    method: 'POST',
    data: params,
  });
}

/**
 * 报告内容更新数据重新跑
 */
export async function editReport(params: API.EditReportParams) {
  return request('/api/v1/log/roleAgent/editReport', {
    method: 'POST',
    data: params,
  });
}

/**
 * 报告内容更新状态
 */
export async function reportUpdateStatus(params: { doc_id_list: string[] }) {
  return request<API.ReportUpdateStatusResponse[]>('/api/v1/log/roleAgent/reportUpdateStatus', {
    method: 'POST',
    data: params,
  });
}

/**
 * 标记消息为已读
 */
export async function markRead(params: { message_ids: number[] }) {
  return request('/api/v1/messages/markRead', {
    method: 'POST',
    data: params,
  });
}

/**
 * 查询单个角色代理日志数据
 */
export async function getRoleAgentDetail<T>(params: { es_id: string }) {
  return request<T>('/api/v1/log/roleAgent/getById', {
    method: 'POST',
    data: params,
  });
}

/**
 * 审批报告
 */
export async function approveReport(params: {
  es_id: string;
  profile_id: string;
}) {
  return request('/api/v1/log/roleAgent/approveReport', {
    method: 'POST',
    data: params,
  });
}

/** 获取所有报告日期 和 版本号 */
export async function getReportDatesWithVer(params: {
  asin: string;
  job_id: string;
  profile_id: string;
}) {
  return request<API.GetAllReportDatesResponse>('/api/v1/log/roleAgent/getReportDatesWithVer', {
    method: 'POST',
    data: params,
  });
}

/** 获取日内报告数据 */
export async function getDaypartReports(params: {
  asin?: string;
  current_time?: string;
  profile_id?: number;
}) {
  return request<{ ads_data_hod: API.TAds_data_hod[], daypart_data: any[] }>('/api/v1/log/roleAgent/daypart_reports', {
    method: 'GET',
    params: params,
  });
}

/** 提交问题反馈 */
export async function submitFeedback(params: {
  parent_asin: string;
  profile_id: string;
  job_id: string;
  ver: string;
  current_time: string;
  content: string;
}) {
  return request('/api/v1/log/feedback/save', {
    method: 'POST',
    data: params,
  });
}

/** 设置默认报告版本 */
export async function setDefaultReport(params: {
  asin: string;
  current_time: string;
  job_id: string;
  profile_id: string;
  ver: number;
}) {
  return request('/api/v1/log/roleAgent/setDefaultReport', {
    method: 'POST',
    data: params,
  });
}

/** 获取广告趋势 */
export async function getAdsTrend(params: {
  asins: string[];
  parent_asin: string;
  profile_id: number;
  start_time: string;
  end_time: string;
  type: 'month' | 'week' | 'day';
}) {
  return request<API.AdsTrendResponse>('/api/v1/ads/listing/adsTrend', {
    method: 'POST',
    data: params,
  });
}

/**
 * 获取广告活动每日数据
 */
export async function getCampaignDailyData(params: GetCampaignDailyDataParams) {
  return request<DailyTableRecord[]>('/api/v1/campaign/dailyData', {
    method: 'GET',
    params,
  });
}

/**
 * 获取广告活动环比分析数据
 */
export async function getCampaignSequentialAnalysis(
  params: GetCampaignSequentialAnalysisParams,
) {
  return request<SequentialAnalysisData>('/api/v1/ads/campaignReport/getCampaignMoM', {
    method: 'POST',
    data: params,
  });
}

/**
 * 获取广告活动同步分析数据
 */
export async function getCampaignSynchronousAnalysis(params: GetCampaignSynchronousAnalysisParams) {
  return request<SynchronousAnalysisData>('/api/v1/ads/campaignReport/getCampaignYoY', {
    method: 'POST',
    data: params,
  });
}
